import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  Row,
  Col,
  Typography,
  Tag,
  Progress,
  Spin,
  Alert,
  Button,
  Space,
  Tooltip,
  Divider
} from 'antd'
import {
  StarOutlined,
  ReloadOutlined,
  <PERSON>boltOutlined,
  TrophyOutlined,
  SafetyOutlined,
  BulbOutlined
} from '@ant-design/icons'
import { apiRequest } from '../utils/api'

const { Title, Text } = Typography

interface RecommendationData {
  number: number
  confidence: number
  reason: string
  risk_level: string
  star_rating: number
}

interface PositionRecommendation {
  position: string
  recommendations: RecommendationData[]
  analysis_summary: {
    historical_trends: string
    shap_insights: string
    total_analyzed_numbers: number
  }
  timestamp: string
}

interface DailyRecommendations {
  timestamp: string
  algorithm_version: string
  positions: {
    hundreds: PositionRecommendation
    tens: PositionRecommendation
    units: PositionRecommendation
  }
  combination: {
    combination: string
    numbers: number[]
    confidence: number
    risk_level: string
    star_rating: number
    recommendation: string
  }
  overall_confidence: number
  status: string
}

const SmartRecommendationCards: React.FC = () => {
  const [recommendations, setRecommendations] = useState<DailyRecommendations | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchRecommendations = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await apiRequest('/api/smart-recommend/daily')
      if (response.status === 'success') {
        setRecommendations(response.data)
      } else {
        setError('获取推荐失败')
      }
    } catch (err) {
      console.error('获取智能推荐失败:', err)
      setError('网络请求失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchRecommendations()
  }, [])

  const renderStarRating = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarOutlined 
        key={i} 
        style={{ 
          color: i < rating ? '#faad14' : '#d9d9d9',
          fontSize: '16px',
          margin: '0 1px'
        }} 
      />
    ))
  }

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case '低风险': return 'green'
      case '中等风险': return 'orange'
      case '高风险': return 'red'
      default: return 'blue'
    }
  }

  const getPositionName = (position: string) => {
    switch (position) {
      case 'hundreds': return '百位'
      case 'tens': return '十位'
      case 'units': return '个位'
      default: return position
    }
  }

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>
            <Text>正在生成智能推荐...</Text>
          </div>
        </div>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <Alert
          message="获取推荐失败"
          description={error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={fetchRecommendations}>
              重试
            </Button>
          }
        />
      </Card>
    )
  }

  if (!recommendations) {
    return (
      <Card>
        <Alert
          message="暂无推荐数据"
          description="请点击刷新获取最新推荐"
          type="info"
          showIcon
          action={
            <Button size="small" onClick={fetchRecommendations}>
              获取推荐
            </Button>
          }
        />
      </Card>
    )
  }

  return (
    <div>
      {/* 头部信息 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <ThunderboltOutlined style={{ color: '#1890ff', fontSize: '20px' }} />
              <Title level={4} style={{ margin: 0 }}>
                🎯 今日智能推荐
              </Title>
              <Tag color="blue">算法版本 {recommendations.algorithm_version}</Tag>
            </Space>
          </Col>
          <Col>
            <Space>
              <Text type="secondary">
                更新时间: {new Date(recommendations.timestamp).toLocaleString()}
              </Text>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={fetchRecommendations}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 整体信心度 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={8}>
            <div style={{ textAlign: 'center' }}>
              <Progress
                type="circle"
                percent={Math.round(recommendations.overall_confidence * 100)}
                format={percent => `${percent}%`}
                strokeColor="#1890ff"
                size={100}
              />
              <div style={{ marginTop: 8 }}>
                <Text strong>整体信心度</Text>
              </div>
            </div>
          </Col>
          <Col span={16}>
            <div>
              <Title level={5}>
                <TrophyOutlined style={{ color: '#faad14' }} /> 推荐组合
              </Title>
              <div style={{ fontSize: '32px', fontWeight: 'bold', color: '#722ed1', marginBottom: 8 }}>
                {recommendations.combination.combination}
              </div>
              <Space>
                <Tag color={getRiskColor(recommendations.combination.risk_level)}>
                  {recommendations.combination.risk_level}
                </Tag>
                <div>
                  {renderStarRating(recommendations.combination.star_rating)}
                </div>
              </Space>
              <div style={{ marginTop: 8 }}>
                <Text type="secondary">{recommendations.combination.recommendation}</Text>
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 各位置推荐 */}
      <Row gutter={16}>
        {Object.entries(recommendations.positions).map(([position, data]) => (
          <Col span={8} key={position}>
            <Card 
              title={
                <Space>
                  <BulbOutlined style={{ color: '#1890ff' }} />
                  {getPositionName(position)}推荐
                </Space>
              }
              extra={
                <Tooltip title={data.analysis_summary.shap_insights}>
                  <SafetyOutlined style={{ color: '#52c41a' }} />
                </Tooltip>
              }
              style={{ marginBottom: 16 }}
            >
              {data.recommendations.map((rec, index) => (
                <div key={index} style={{ marginBottom: index < data.recommendations.length - 1 ? 12 : 0 }}>
                  <Row justify="space-between" align="middle">
                    <Col>
                      <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                        #{index + 1} {rec.number}
                      </div>
                    </Col>
                    <Col>
                      <div style={{ textAlign: 'right' }}>
                        <div>{renderStarRating(rec.star_rating)}</div>
                        <Tag color={getRiskColor(rec.risk_level)} size="small">
                          {rec.risk_level}
                        </Tag>
                      </div>
                    </Col>
                  </Row>
                  <div style={{ marginTop: 4 }}>
                    <Progress 
                      percent={Math.round(rec.confidence * 100)} 
                      size="small" 
                      strokeColor="#52c41a"
                      showInfo={false}
                    />
                  </div>
                  <div style={{ marginTop: 4 }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {rec.reason}
                    </Text>
                  </div>
                  {index < data.recommendations.length - 1 && <Divider style={{ margin: '12px 0' }} />}
                </div>
              ))}
            </Card>
          </Col>
        ))}
      </Row>

      {/* 分析摘要 */}
      <Card title="📊 分析摘要" style={{ marginTop: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <div>
              <Text strong>历史趋势分析</Text>
              <div style={{ marginTop: 4 }}>
                <Text type="secondary">
                  {recommendations.positions.hundreds.analysis_summary.historical_trends}
                </Text>
              </div>
            </div>
          </Col>
          <Col span={8}>
            <div>
              <Text strong>SHAP智能分析</Text>
              <div style={{ marginTop: 4 }}>
                <Text type="secondary">
                  {recommendations.positions.hundreds.analysis_summary.shap_insights}
                </Text>
              </div>
            </div>
          </Col>
          <Col span={8}>
            <div>
              <Text strong>分析数据量</Text>
              <div style={{ marginTop: 4 }}>
                <Text type="secondary">
                  共分析 {recommendations.positions.hundreds.analysis_summary.total_analyzed_numbers} 个数字特征
                </Text>
              </div>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  )
}

export default SmartRecommendationCards
