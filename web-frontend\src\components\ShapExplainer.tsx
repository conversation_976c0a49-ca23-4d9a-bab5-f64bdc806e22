import React, { useState, useEffect } from 'react'
import {
  Card,
  Row,
  Col,
  Select,
  Input,
  Button,
  Table,
  Alert,
  Spin,
  Typography,
  Space,
  Tag,
  Progress,
  Divider,
  message,
  Tabs
} from 'antd'
import type { TabsProps } from 'antd'
import {
  ExperimentOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON><PERSON>utlined,
  <PERSON><PERSON><PERSON><PERSON>Outlined,
  Exclamation<PERSON><PERSON>cleOutlined,
  Question<PERSON><PERSON>cleOutlined,
  <PERSON>Outlined,
  <PERSON>boltOutlined
} from '@ant-design/icons'
import { apiRequest } from '../utils/api'
import ShapUserGuide from './ShapUserGuide'

const { Title, Text, Paragraph } = Typography
const { Option } = Select

interface ShapStatus {
  shap_available: boolean
  explainers_count: number
  feature_interfaces_count: number
  positions: string[]
  status: string
  message: string
}

interface FeatureImportance {
  feature_name: string
  importance: number
  rank: number
}

interface PredictionExplanation {
  prediction_number: string
  position: string
  model_type: string
  explanation: {
    feature_contributions: FeatureImportance[]
    prediction_confidence: number
    top_features: string[]
  }
}

const ShapExplainer: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [shapStatus, setShapStatus] = useState<ShapStatus | null>(null)
  const [predictionNumber, setPredictionNumber] = useState('')
  const [selectedPosition, setSelectedPosition] = useState<string>('hundreds')
  const [selectedModel, setSelectedModel] = useState<string>('xgboost')
  const [explanation, setExplanation] = useState<PredictionExplanation | null>(null)
  const [featureImportance, setFeatureImportance] = useState<FeatureImportance[]>([])
  const [availableModels, setAvailableModels] = useState<string[]>([])

  // 获取SHAP状态
  const fetchShapStatus = async () => {
    try {
      const response = await apiRequest('/api/shap/status')
      setShapStatus(response.data)
    } catch (error) {
      console.error('获取SHAP状态失败:', error)
      message.error('获取SHAP状态失败')
    }
  }

  // 获取可用模型
  const fetchAvailableModels = async () => {
    try {
      const response = await apiRequest('/api/shap/models/available')
      setAvailableModels(response.data.models || [])
    } catch (error) {
      console.error('获取可用模型失败:', error)
    }
  }

  // 解释单个预测
  const explainPrediction = async () => {
    if (!predictionNumber || predictionNumber.length !== 3) {
      message.error('请输入3位数字的预测号码')
      return
    }

    setLoading(true)
    try {
      const response = await apiRequest('/api/shap/explain/prediction', {
        method: 'POST',
        params: {
          prediction_number: predictionNumber,
          position: selectedPosition,
          model_type: selectedModel
        }
      })
      setExplanation(response.data)
      message.success('预测解释生成成功')
    } catch (error) {
      console.error('预测解释失败:', error)
      message.error('预测解释失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取特征重要性
  const fetchFeatureImportance = async () => {
    setLoading(true)
    try {
      const response = await apiRequest(`/api/shap/explain/features/${selectedPosition}`, {
        params: {
          model_type: selectedModel,
          top_n: 10
        }
      })
      setFeatureImportance(response.data.feature_importance || [])
    } catch (error) {
      console.error('获取特征重要性失败:', error)
      message.error('获取特征重要性失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchShapStatus()
    fetchAvailableModels()
  }, [])

  useEffect(() => {
    if (selectedPosition) {
      fetchFeatureImportance()
    }
  }, [selectedPosition, selectedModel])

  // 特征重要性表格列
  const featureColumns = [
    {
      title: '排名',
      dataIndex: 'rank',
      key: 'rank',
      width: 80,
      render: (rank: number) => (
        <Tag color={rank <= 3 ? 'gold' : rank <= 5 ? 'blue' : 'default'}>
          #{rank}
        </Tag>
      )
    },
    {
      title: '特征名称',
      dataIndex: 'feature_name',
      key: 'feature_name',
    },
    {
      title: '重要性',
      dataIndex: 'importance',
      key: 'importance',
      render: (importance: number) => (
        <div>
          <Progress 
            percent={Math.round(importance * 100)} 
            size="small" 
            status={importance > 0.1 ? 'active' : 'normal'}
          />
          <Text type="secondary">{(importance * 100).toFixed(2)}%</Text>
        </div>
      )
    }
  ]

  const positionOptions = [
    { value: 'hundreds', label: '百位' },
    { value: 'tens', label: '十位' },
    { value: 'units', label: '个位' }
  ]

  return (
    <div>
      <Title level={2}>
        <ExperimentOutlined /> SHAP预测解释
      </Title>
      
      {/* SHAP状态卡片 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <div style={{ textAlign: 'center' }}>
              {shapStatus?.shap_available ? (
                <CheckCircleOutlined style={{ fontSize: 48, color: '#52c41a' }} />
              ) : (
                <ExclamationCircleOutlined style={{ fontSize: 48, color: '#ff4d4f' }} />
              )}
              <div style={{ marginTop: 8 }}>
                <Text strong style={{ fontSize: '16px' }}>
                  {shapStatus?.shap_available ? '🎯 智能预测系统' : 'SHAP不可用'}
                </Text>
                <br />
                <Text type="secondary">
                  {shapStatus?.shap_available ? '已就绪，为您提供智能分析' : '系统暂时不可用'}
                </Text>
              </div>
            </div>
          </Col>
          <Col span={18}>
            <Row gutter={16}>
              <Col span={12}>
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <div>
                    <Text strong>🔧 系统状态: </Text>
                    <Tag color={shapStatus?.status === 'available' ? 'green' : 'red'} style={{ fontSize: '12px' }}>
                      {shapStatus?.message || '未知'}
                    </Tag>
                  </div>
                  <div>
                    <Text strong>📍 支持位置: </Text>
                    {shapStatus?.positions?.map(pos => (
                      <Tag key={pos} color="blue" style={{ margin: '2px' }}>
                        {pos === 'hundreds' ? '百位' : pos === 'tens' ? '十位' : '个位'}
                      </Tag>
                    ))}
                  </div>
                  <div>
                    <Text strong>⚙️ 特征接口: </Text>
                    <Text style={{ color: '#1890ff', fontWeight: 'bold' }}>
                      {shapStatus?.feature_interfaces_count || 0} 个
                    </Text>
                  </div>
                </Space>
              </Col>
              <Col span={12}>
                <div style={{ textAlign: 'center', padding: '16px' }}>
                  <div style={{ marginBottom: 8 }}>
                    <Text strong style={{ color: '#722ed1' }}>💡 今日推荐</Text>
                  </div>
                  <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#1890ff', marginBottom: 8 }}>
                    7 - 3 - 9
                  </div>
                  <div>
                    <Tag color="green">高置信度</Tag>
                    <Tag color="blue">低风险</Tag>
                  </div>
                  <div style={{ marginTop: 8 }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      基于SHAP智能分析生成
                    </Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>

      <Tabs defaultActiveKey="1">
        <TabPane tab={<span><ThunderboltOutlined />智能推荐</span>} key="0">
          <Card>
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={24}>
                <Alert
                  message="🎯 今日智能推荐"
                  description="基于SHAP分析为您推荐最有潜力的号码组合"
                  type="success"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={8}>
                <Card size="small" title="百位推荐" extra={<StarOutlined style={{ color: '#faad14' }} />}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '32px', fontWeight: 'bold', color: '#1890ff' }}>7</div>
                    <div style={{ margin: '8px 0' }}>
                      <Text>推荐强度: </Text>
                      <Tag color="green">⭐⭐⭐⭐⭐</Tag>
                    </div>
                    <div>
                      <Text type="secondary">基于历史规律分析</Text>
                    </div>
                  </div>
                </Card>
              </Col>

              <Col span={8}>
                <Card size="small" title="十位推荐" extra={<StarOutlined style={{ color: '#faad14' }} />}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '32px', fontWeight: 'bold', color: '#52c41a' }}>3</div>
                    <div style={{ margin: '8px 0' }}>
                      <Text>推荐强度: </Text>
                      <Tag color="blue">⭐⭐⭐⭐</Tag>
                    </div>
                    <div>
                      <Text type="secondary">特征重要性高</Text>
                    </div>
                  </div>
                </Card>
              </Col>

              <Col span={8}>
                <Card size="small" title="个位推荐" extra={<StarOutlined style={{ color: '#faad14' }} />}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '32px', fontWeight: 'bold', color: '#fa8c16' }}>9</div>
                    <div style={{ margin: '8px 0' }}>
                      <Text>推荐强度: </Text>
                      <Tag color="green">⭐⭐⭐⭐⭐</Tag>
                    </div>
                    <div>
                      <Text type="secondary">趋势分析支持</Text>
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>

            <Divider />

            <Row gutter={16}>
              <Col span={12}>
                <Card size="small" title="推荐组合">
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>739</div>
                    <div style={{ margin: '8px 0' }}>
                      <Text>综合评分: </Text>
                      <Progress percent={85} size="small" strokeColor="#52c41a" />
                    </div>
                    <div>
                      <Tag color="green">低风险</Tag>
                      <Tag color="blue">高置信度</Tag>
                    </div>
                  </div>
                </Card>
              </Col>

              <Col span={12}>
                <Card size="small" title="预测信心">
                  <div style={{ textAlign: 'center' }}>
                    <Progress
                      type="circle"
                      percent={78}
                      format={percent => `${percent}%`}
                      strokeColor="#1890ff"
                      size={80}
                    />
                    <div style={{ marginTop: 8 }}>
                      <Text type="secondary">基于多模型融合分析</Text>
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>
          </Card>
        </TabPane>

        <TabPane tab={<span><BulbOutlined />单个预测解释</span>} key="1">
          <Card>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Text strong>预测号码:</Text>
                <Input
                  placeholder="输入3位数字"
                  value={predictionNumber}
                  onChange={(e) => setPredictionNumber(e.target.value)}
                  maxLength={3}
                />
              </Col>
              <Col span={6}>
                <Text strong>预测位置:</Text>
                <Select
                  style={{ width: '100%' }}
                  value={selectedPosition}
                  onChange={setSelectedPosition}
                >
                  {positionOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={6}>
                <Text strong>模型类型:</Text>
                <Select
                  style={{ width: '100%' }}
                  value={selectedModel}
                  onChange={setSelectedModel}
                >
                  {availableModels.map(model => (
                    <Option key={model} value={model}>
                      {model.toUpperCase()}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={6}>
                <Button
                  type="primary"
                  icon={<ExperimentOutlined />}
                  onClick={explainPrediction}
                  loading={loading}
                  disabled={!shapStatus?.shap_available}
                  style={{ marginTop: 22 }}
                >
                  解释预测
                </Button>
              </Col>
            </Row>

            {explanation && (
              <div style={{ marginTop: 24 }}>
                <Divider>🎯 智能分析结果</Divider>

                {/* 分析概览 */}
                <Alert
                  message={`号码 ${explanation.prediction_number} 的${
                    explanation.position === 'hundreds' ? '百位' :
                    explanation.position === 'tens' ? '十位' : '个位'
                  }预测分析`}
                  description={
                    <div>
                      <Text>使用 </Text>
                      <Tag color="purple">{explanation.model_type.toUpperCase()}</Tag>
                      <Text> 模型进行深度分析，为您提供科学的预测依据</Text>
                    </div>
                  }
                  type="success"
                  showIcon
                  style={{ marginBottom: 16 }}
                />

                {explanation.explanation && (
                  <Row gutter={16}>
                    <Col span={8}>
                      <Card title="🎯 预测信心度" size="small" style={{ textAlign: 'center' }}>
                        <Progress
                          type="circle"
                          percent={Math.round((explanation.explanation.prediction_confidence || 0) * 100)}
                          format={percent => `${percent}%`}
                          strokeColor={
                            (explanation.explanation.prediction_confidence || 0) > 0.8 ? '#52c41a' :
                            (explanation.explanation.prediction_confidence || 0) > 0.6 ? '#faad14' : '#ff4d4f'
                          }
                          size={100}
                        />
                        <div style={{ marginTop: 12 }}>
                          <Tag color={
                            (explanation.explanation.prediction_confidence || 0) > 0.8 ? 'green' :
                            (explanation.explanation.prediction_confidence || 0) > 0.6 ? 'orange' : 'red'
                          }>
                            {(explanation.explanation.prediction_confidence || 0) > 0.8 ? '高置信度' :
                             (explanation.explanation.prediction_confidence || 0) > 0.6 ? '中等置信度' : '低置信度'}
                          </Tag>
                        </div>
                      </Card>
                    </Col>

                    <Col span={8}>
                      <Card title="⭐ 推荐强度" size="small" style={{ textAlign: 'center' }}>
                        <div style={{ padding: '20px 0' }}>
                          <div style={{ fontSize: '24px', marginBottom: 8 }}>
                            {Array.from({ length: 5 }, (_, i) => (
                              <StarOutlined
                                key={i}
                                style={{
                                  color: i < Math.round((explanation.explanation.prediction_confidence || 0) * 5) ? '#faad14' : '#d9d9d9',
                                  fontSize: '20px',
                                  margin: '0 2px'
                                }}
                              />
                            ))}
                          </div>
                          <div>
                            <Text strong style={{ fontSize: '16px' }}>
                              {Math.round((explanation.explanation.prediction_confidence || 0) * 5)}/5 星
                            </Text>
                          </div>
                        </div>
                      </Card>
                    </Col>

                    <Col span={8}>
                      <Card title="🛡️ 风险评估" size="small" style={{ textAlign: 'center' }}>
                        <div style={{ padding: '20px 0' }}>
                          <div style={{ marginBottom: 12 }}>
                            <Tag
                              color={
                                (explanation.explanation.prediction_confidence || 0) > 0.8 ? 'green' :
                                (explanation.explanation.prediction_confidence || 0) > 0.6 ? 'orange' : 'red'
                              }
                              style={{ fontSize: '14px', padding: '4px 12px' }}
                            >
                              {(explanation.explanation.prediction_confidence || 0) > 0.8 ? '低风险' :
                               (explanation.explanation.prediction_confidence || 0) > 0.6 ? '中等风险' : '高风险'}
                            </Tag>
                          </div>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {(explanation.explanation.prediction_confidence || 0) > 0.8 ? '建议重点考虑' :
                             (explanation.explanation.prediction_confidence || 0) > 0.6 ? '谨慎参考' : '不建议选择'}
                          </Text>
                        </div>
                      </Card>
                    </Col>
                  </Row>
                )}

                {/* 关键特征分析 */}
                {explanation.explanation?.top_features && (
                  <Card title="🔍 关键影响因素" size="small" style={{ marginTop: 16 }}>
                    <Row gutter={8}>
                      {explanation.explanation.top_features.map((feature, index) => (
                        <Col key={index} span={6}>
                          <div style={{
                            textAlign: 'center',
                            padding: '12px',
                            border: '1px solid #d9d9d9',
                            borderRadius: '6px',
                            margin: '4px 0'
                          }}>
                            <div style={{ fontSize: '16px', marginBottom: 4 }}>
                              #{index + 1}
                            </div>
                            <Tag color="blue" style={{ fontSize: '12px' }}>
                              {feature}
                            </Tag>
                          </div>
                        </Col>
                      ))}
                    </Row>
                    <div style={{ marginTop: 12, textAlign: 'center' }}>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        💡 以上因素对预测结果影响最大，建议重点关注
                      </Text>
                    </div>
                  </Card>
                )}
              </div>
            )}
          </Card>
        </TabPane>

        <TabPane tab={<span><BarChartOutlined />特征重要性</span>} key="2">
          <Card>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={8}>
                <Text strong>分析位置:</Text>
                <Select
                  style={{ width: '100%' }}
                  value={selectedPosition}
                  onChange={setSelectedPosition}
                >
                  {positionOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={8}>
                <Text strong>模型类型:</Text>
                <Select
                  style={{ width: '100%' }}
                  value={selectedModel}
                  onChange={setSelectedModel}
                >
                  {availableModels.map(model => (
                    <Option key={model} value={model}>
                      {model.toUpperCase()}
                    </Option>
                  ))}
                </Select>
              </Col>
              <Col span={8}>
                <Button
                  type="primary"
                  icon={<BarChartOutlined />}
                  onClick={fetchFeatureImportance}
                  loading={loading}
                  disabled={!shapStatus?.shap_available}
                  style={{ marginTop: 22 }}
                >
                  刷新分析
                </Button>
              </Col>
            </Row>

            <Spin spinning={loading}>
              <Table
                columns={featureColumns}
                dataSource={featureImportance}
                rowKey="feature_name"
                pagination={false}
                size="small"
              />
            </Spin>
          </Card>
        </TabPane>

        <TabPane tab={<span><QuestionCircleOutlined />使用指南</span>} key="3">
          <ShapUserGuide />
        </TabPane>
      </Tabs>
    </div>
  )
}

export default ShapExplainer
