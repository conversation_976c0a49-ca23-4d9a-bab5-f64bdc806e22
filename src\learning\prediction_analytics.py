#!/usr/bin/env python3
"""
预测分析引擎
用于分析预测准确率、生成学习洞察、对比策略效果等
"""

import sqlite3
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
import logging

from ..database.learning_data_manager import LearningDataManager, PredictionRecord

logger = logging.getLogger(__name__)


@dataclass
class LearningInsight:
    """学习洞察数据模型"""
    insight_type: str
    title: str
    description: str
    confidence: float
    data: Dict[str, Any]
    recommendations: List[str]


class PredictionAnalytics:
    """预测分析引擎"""
    
    def __init__(self, db_path: str = "data/fucai3d.db"):
        self.db_path = db_path
        self.data_manager = LearningDataManager(db_path)
        
    def analyze_prediction_accuracy(self, days: int = 30) -> Dict[str, Any]:
        """
        分析预测准确率
        
        Args:
            days: 分析天数
            
        Returns:
            Dict: 包含详细准确率分析的字典
        """
        try:
            # 获取基础统计
            base_stats = self.data_manager.get_accuracy_statistics(days)
            
            # 获取预测记录
            end_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d %H:%M:%S')
            
            records = self.data_manager.get_prediction_records(
                limit=1000,
                filters={
                    'start_date': start_date,
                    'end_date': end_date,
                    'has_result': True
                }
            )
            
            if not records:
                return {
                    'status': 'no_data',
                    'message': f'最近{days}天没有完成的预测记录'
                }
            
            # 计算详细准确率
            accuracy_analysis = self._calculate_detailed_accuracy(records)
            
            # 趋势分析
            trend_analysis = self._analyze_accuracy_trends(records, days)
            
            # 模型对比
            model_comparison = self._compare_model_performance(records)
            
            return {
                'status': 'success',
                'period': f'最近{days}天',
                'total_records': len(records),
                'base_statistics': base_stats,
                'detailed_accuracy': accuracy_analysis,
                'trend_analysis': trend_analysis,
                'model_comparison': model_comparison,
                'analysis_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"分析预测准确率失败: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def _calculate_detailed_accuracy(self, records: List[Dict]) -> Dict[str, Any]:
        """计算详细准确率"""
        total_count = len(records)
        if total_count == 0:
            return {}
        
        # 完全命中率
        perfect_hits = sum(1 for r in records if r.get('accuracy_score') == 1.0)
        perfect_hit_rate = perfect_hits / total_count
        
        # 位置准确率
        position_accuracy = {'hundreds': 0, 'tens': 0, 'units': 0}
        position_counts = {'hundreds': 0, 'tens': 0, 'units': 0}
        
        for record in records:
            predicted = record.get('predicted_numbers', '')
            actual = record.get('actual_numbers', '')
            
            if len(predicted) == 3 and len(actual) == 3:
                if predicted[0] == actual[0]:
                    position_accuracy['hundreds'] += 1
                if predicted[1] == actual[1]:
                    position_accuracy['tens'] += 1
                if predicted[2] == actual[2]:
                    position_accuracy['units'] += 1
                
                for pos in position_counts:
                    position_counts[pos] += 1
        
        # 计算位置准确率百分比
        for pos in position_accuracy:
            if position_counts[pos] > 0:
                position_accuracy[pos] = position_accuracy[pos] / position_counts[pos]
        
        # 数字命中率（包含正确数字但位置可能不对）
        number_hit_rate = self._calculate_number_hit_rate(records)
        
        return {
            'perfect_hit_rate': perfect_hit_rate,
            'perfect_hits': perfect_hits,
            'position_accuracy': position_accuracy,
            'number_hit_rate': number_hit_rate,
            'total_analyzed': total_count
        }
    
    def _calculate_number_hit_rate(self, records: List[Dict]) -> float:
        """计算数字命中率"""
        hit_count = 0
        total_count = len(records)
        
        for record in records:
            predicted = record.get('predicted_numbers', '')
            actual = record.get('actual_numbers', '')
            
            if len(predicted) == 3 and len(actual) == 3:
                predicted_digits = set(predicted)
                actual_digits = set(actual)
                
                # 计算交集
                intersection = predicted_digits.intersection(actual_digits)
                if len(intersection) > 0:
                    hit_count += len(intersection) / 3  # 按比例计算
        
        return hit_count / total_count if total_count > 0 else 0
    
    def _analyze_accuracy_trends(self, records: List[Dict], days: int) -> Dict[str, Any]:
        """分析准确率趋势"""
        # 按日期分组
        daily_stats = {}
        
        for record in records:
            date_str = record.get('prediction_date', '')[:10]  # 取日期部分
            
            if date_str not in daily_stats:
                daily_stats[date_str] = {
                    'total': 0,
                    'perfect_hits': 0,
                    'accuracy_scores': []
                }
            
            daily_stats[date_str]['total'] += 1
            
            accuracy_score = record.get('accuracy_score')
            if accuracy_score is not None:
                daily_stats[date_str]['accuracy_scores'].append(accuracy_score)
                if accuracy_score == 1.0:
                    daily_stats[date_str]['perfect_hits'] += 1
        
        # 计算每日准确率
        trend_data = []
        for date_str, stats in sorted(daily_stats.items()):
            avg_accuracy = np.mean(stats['accuracy_scores']) if stats['accuracy_scores'] else 0
            perfect_rate = stats['perfect_hits'] / stats['total'] if stats['total'] > 0 else 0
            
            trend_data.append({
                'date': date_str,
                'total_predictions': stats['total'],
                'average_accuracy': avg_accuracy,
                'perfect_hit_rate': perfect_rate
            })
        
        # 计算趋势方向
        if len(trend_data) >= 2:
            recent_accuracy = np.mean([d['average_accuracy'] for d in trend_data[-7:]])  # 最近7天
            earlier_accuracy = np.mean([d['average_accuracy'] for d in trend_data[:-7]])  # 之前的天数
            
            trend_direction = 'improving' if recent_accuracy > earlier_accuracy else 'declining'
            trend_strength = abs(recent_accuracy - earlier_accuracy)
        else:
            trend_direction = 'stable'
            trend_strength = 0
        
        return {
            'daily_data': trend_data,
            'trend_direction': trend_direction,
            'trend_strength': trend_strength,
            'analysis_period': f'{days}天'
        }
    
    def _compare_model_performance(self, records: List[Dict]) -> Dict[str, Any]:
        """对比模型性能"""
        model_stats = {}
        
        for record in records:
            model_source = record.get('model_source', 'unknown')
            
            if model_source not in model_stats:
                model_stats[model_source] = {
                    'total_predictions': 0,
                    'perfect_hits': 0,
                    'accuracy_scores': [],
                    'confidence_levels': []
                }
            
            stats = model_stats[model_source]
            stats['total_predictions'] += 1
            
            accuracy_score = record.get('accuracy_score')
            if accuracy_score is not None:
                stats['accuracy_scores'].append(accuracy_score)
                if accuracy_score == 1.0:
                    stats['perfect_hits'] += 1
            
            confidence_level = record.get('confidence_level')
            if confidence_level is not None:
                stats['confidence_levels'].append(confidence_level)
        
        # 计算每个模型的性能指标
        model_performance = {}
        for model, stats in model_stats.items():
            avg_accuracy = np.mean(stats['accuracy_scores']) if stats['accuracy_scores'] else 0
            avg_confidence = np.mean(stats['confidence_levels']) if stats['confidence_levels'] else 0
            perfect_rate = stats['perfect_hits'] / stats['total_predictions'] if stats['total_predictions'] > 0 else 0
            
            model_performance[model] = {
                'total_predictions': stats['total_predictions'],
                'average_accuracy': avg_accuracy,
                'perfect_hit_rate': perfect_rate,
                'average_confidence': avg_confidence,
                'performance_score': avg_accuracy * 0.7 + perfect_rate * 0.3  # 综合评分
            }
        
        # 排序模型
        sorted_models = sorted(
            model_performance.items(),
            key=lambda x: x[1]['performance_score'],
            reverse=True
        )
        
        return {
            'model_rankings': sorted_models,
            'best_model': sorted_models[0][0] if sorted_models else None,
            'model_details': model_performance
        }
    
    def generate_learning_insights(self, user_id: str = None) -> List[LearningInsight]:
        """
        生成学习洞察
        
        Args:
            user_id: 用户ID（可选，用于个性化洞察）
            
        Returns:
            List[LearningInsight]: 学习洞察列表
        """
        insights = []
        
        try:
            # 分析最近30天的数据
            analysis = self.analyze_prediction_accuracy(30)
            
            if analysis.get('status') != 'success':
                return insights
            
            # 生成准确率洞察
            accuracy_insight = self._generate_accuracy_insight(analysis)
            if accuracy_insight:
                insights.append(accuracy_insight)
            
            # 生成趋势洞察
            trend_insight = self._generate_trend_insight(analysis)
            if trend_insight:
                insights.append(trend_insight)
            
            # 生成模型选择洞察
            model_insight = self._generate_model_insight(analysis)
            if model_insight:
                insights.append(model_insight)
            
        except Exception as e:
            logger.error(f"生成学习洞察失败: {e}")
        
        return insights

    def _generate_accuracy_insight(self, analysis: Dict) -> Optional[LearningInsight]:
        """生成准确率洞察"""
        detailed_accuracy = analysis.get('detailed_accuracy', {})
        perfect_hit_rate = detailed_accuracy.get('perfect_hit_rate', 0)
        position_accuracy = detailed_accuracy.get('position_accuracy', {})

        if perfect_hit_rate > 0.1:  # 10%以上完全命中率
            return LearningInsight(
                insight_type='accuracy_high',
                title='预测准确率表现优秀',
                description=f'您的完全命中率达到{perfect_hit_rate:.1%}，表现优秀！',
                confidence=0.9,
                data={'perfect_hit_rate': perfect_hit_rate, 'position_accuracy': position_accuracy},
                recommendations=[
                    '继续使用当前的预测策略',
                    '可以适当提高投注信心',
                    '记录成功经验以便复制'
                ]
            )
        elif perfect_hit_rate < 0.05:  # 5%以下完全命中率
            best_position = max(position_accuracy.items(), key=lambda x: x[1]) if position_accuracy else None

            recommendations = ['分析失败原因，调整预测策略']
            if best_position and best_position[1] > 0.3:
                recommendations.append(f'您在{best_position[0]}位置表现较好，可以重点关注')

            return LearningInsight(
                insight_type='accuracy_low',
                title='预测准确率需要改进',
                description=f'完全命中率仅为{perfect_hit_rate:.1%}，建议优化策略',
                confidence=0.8,
                data={'perfect_hit_rate': perfect_hit_rate, 'position_accuracy': position_accuracy},
                recommendations=recommendations
            )

        return None

    def _generate_trend_insight(self, analysis: Dict) -> Optional[LearningInsight]:
        """生成趋势洞察"""
        trend_analysis = analysis.get('trend_analysis', {})
        trend_direction = trend_analysis.get('trend_direction', 'stable')
        trend_strength = trend_analysis.get('trend_strength', 0)

        if trend_direction == 'improving' and trend_strength > 0.1:
            return LearningInsight(
                insight_type='trend_improving',
                title='预测能力持续提升',
                description='您的预测准确率呈上升趋势，继续保持！',
                confidence=0.8,
                data=trend_analysis,
                recommendations=[
                    '保持当前的学习节奏',
                    '总结最近的成功经验',
                    '可以尝试更复杂的预测策略'
                ]
            )
        elif trend_direction == 'declining' and trend_strength > 0.1:
            return LearningInsight(
                insight_type='trend_declining',
                title='预测准确率下降',
                description='最近的预测准确率有所下降，需要调整策略',
                confidence=0.8,
                data=trend_analysis,
                recommendations=[
                    '回顾最近的预测失误',
                    '考虑回到之前成功的策略',
                    '减少预测频率，提高质量'
                ]
            )

        return None

    def _generate_model_insight(self, analysis: Dict) -> Optional[LearningInsight]:
        """生成模型选择洞察"""
        model_comparison = analysis.get('model_comparison', {})
        model_rankings = model_comparison.get('model_rankings', [])

        if len(model_rankings) >= 2:
            best_model = model_rankings[0]
            worst_model = model_rankings[-1]

            performance_gap = best_model[1]['performance_score'] - worst_model[1]['performance_score']

            if performance_gap > 0.2:  # 20%以上性能差距
                return LearningInsight(
                    insight_type='model_selection',
                    title='模型性能差异显著',
                    description=f'{best_model[0]}模型表现最佳，建议优先使用',
                    confidence=0.9,
                    data=model_comparison,
                    recommendations=[
                        f'优先使用{best_model[0]}模型进行预测',
                        f'减少使用{worst_model[0]}模型',
                        '分析不同模型的适用场景'
                    ]
                )

        return None
