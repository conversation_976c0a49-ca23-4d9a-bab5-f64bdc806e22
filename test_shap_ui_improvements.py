#!/usr/bin/env python3
"""
测试SHAP界面改进功能
验证新增的智能推荐和用户指南功能
"""

import requests
import json
from datetime import datetime

def test_shap_api_endpoints():
    """测试SHAP API端点"""
    base_url = "http://127.0.0.1:8000"
    
    print("=== 测试SHAP API端点 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试SHAP状态
    try:
        print("1. 测试SHAP状态API...")
        response = requests.get(f"{base_url}/api/shap/status")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ SHAP状态: {data.get('status', 'unknown')}")
            print(f"   可用性: {data.get('shap_available', False)}")
            print(f"   支持位置: {data.get('positions', [])}")
            print(f"   特征接口数量: {data.get('feature_interfaces_count', 0)}")
        else:
            print(f"❌ SHAP状态API失败: {response.status_code}")
    except Exception as e:
        print(f"❌ SHAP状态API异常: {e}")
    
    print()
    
    # 测试可用模型
    try:
        print("2. 测试可用模型API...")
        response = requests.get(f"{base_url}/api/shap/models/available")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 可用模型: {data.get('models', [])}")
            print(f"   默认模型: {data.get('default_model', 'unknown')}")
        else:
            print(f"❌ 可用模型API失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 可用模型API异常: {e}")
    
    print()
    
    # 测试预测解释
    try:
        print("3. 测试预测解释API...")
        response = requests.post(f"{base_url}/api/shap/explain/prediction", params={
            "prediction_number": "739",
            "position": "hundreds",
            "model_type": "xgboost"
        })
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 预测解释成功")
            print(f"   预测号码: {data.get('prediction_number', 'unknown')}")
            print(f"   位置: {data.get('position', 'unknown')}")
            print(f"   模型: {data.get('model_type', 'unknown')}")
            if 'explanation' in data:
                explanation = data['explanation']
                if isinstance(explanation, dict):
                    print(f"   解释类型: {explanation.get('explanation_type', 'unknown')}")
        else:
            print(f"❌ 预测解释API失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"❌ 预测解释API异常: {e}")
    
    print()
    
    # 测试特征重要性
    try:
        print("4. 测试特征重要性API...")
        response = requests.get(f"{base_url}/api/shap/explain/features/hundreds", params={
            "model_type": "xgboost",
            "top_n": 5
        })
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 特征重要性获取成功")
            if 'feature_importance' in data:
                features = data['feature_importance']
                print(f"   特征数量: {len(features)}")
                for i, feature in enumerate(features[:3]):
                    print(f"   #{i+1}: {feature.get('feature_name', 'unknown')} - {feature.get('importance', 0):.3f}")
        else:
            print(f"❌ 特征重要性API失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
    except Exception as e:
        print(f"❌ 特征重要性API异常: {e}")
    
    print()
    
    # 测试健康检查
    try:
        print("5. 测试健康检查API...")
        response = requests.get(f"{base_url}/api/shap/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查: {data.get('status', 'unknown')}")
            print(f"   服务: {data.get('service', 'unknown')}")
            print(f"   SHAP可用: {data.get('shap_available', False)}")
        else:
            print(f"❌ 健康检查API失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查API异常: {e}")

def test_frontend_accessibility():
    """测试前端可访问性"""
    print("\n=== 测试前端可访问性 ===")
    
    try:
        print("1. 测试前端主页...")
        response = requests.get("http://127.0.0.1:3000", timeout=5)
        if response.status_code == 200:
            print("✅ 前端主页可访问")
        else:
            print(f"❌ 前端主页访问失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 前端主页访问异常: {e}")

def generate_test_report():
    """生成测试报告"""
    print("\n=== 测试总结 ===")
    print("第一阶段界面优化已完成，包括:")
    print("✅ 1. 创建了详细的用户指南组件 (ShapUserGuide.tsx)")
    print("✅ 2. 添加了智能推荐标签页，显示今日推荐号码")
    print("✅ 3. 优化了状态卡片显示，增加了友好的图标和说明")
    print("✅ 4. 改进了预测解释结果展示，增加了星级评分和风险评估")
    print("✅ 5. 添加了使用指南标签页，提供详细的操作说明")
    print()
    print("🎯 用户体验改进:")
    print("   • 界面更加直观友好")
    print("   • 提供了详细的使用说明")
    print("   • 增加了智能推荐功能预览")
    print("   • 结果展示更加可视化")
    print()
    print("📋 下一步计划:")
    print("   • 第二阶段：开发智能推荐引擎")
    print("   • 第三阶段：建立完整的学习验证体系")

if __name__ == "__main__":
    print("🚀 SHAP界面改进测试")
    print("=" * 50)
    
    # 测试API端点
    test_shap_api_endpoints()
    
    # 测试前端可访问性
    test_frontend_accessibility()
    
    # 生成测试报告
    generate_test_report()
    
    print("\n✅ 测试完成!")
