"""
智能推荐API路由
提供基于SHAP分析的智能推荐服务
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.fusion.smart_recommendation_engine import SmartRecommendationEngine
except ImportError as e:
    logging.warning(f"无法导入智能推荐引擎: {e}")
    SmartRecommendationEngine = None

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/smart-recommend", tags=["智能推荐"])

# 全局推荐引擎实例
_recommendation_engine = None

def get_recommendation_engine():
    """获取推荐引擎实例"""
    global _recommendation_engine
    if _recommendation_engine is None and SmartRecommendationEngine is not None:
        try:
            _recommendation_engine = SmartRecommendationEngine()
            logger.info("智能推荐引擎初始化成功")
        except Exception as e:
            logger.error(f"智能推荐引擎初始化失败: {e}")
            _recommendation_engine = None
    return _recommendation_engine

@router.get("/status")
async def get_recommendation_status():
    """
    获取智能推荐系统状态
    
    Returns:
        Dict: 系统状态信息
    """
    try:
        engine = get_recommendation_engine()
        
        if engine is None:
            return {
                "status": "unavailable",
                "message": "智能推荐引擎不可用",
                "recommendation_available": False,
                "timestamp": datetime.now().isoformat()
            }
        
        return {
            "status": "available",
            "message": "智能推荐系统正常运行",
            "recommendation_available": True,
            "algorithm_version": engine.algorithm_version,
            "supported_positions": ["hundreds", "tens", "units"],
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取推荐状态失败: {e}")
        return {
            "status": "error",
            "message": f"状态检查失败: {str(e)}",
            "recommendation_available": False,
            "timestamp": datetime.now().isoformat()
        }

@router.get("/daily")
async def get_daily_recommendations():
    """
    获取今日智能推荐
    
    Returns:
        Dict: 今日完整推荐结果
    """
    try:
        engine = get_recommendation_engine()
        if engine is None:
            raise HTTPException(status_code=503, detail="智能推荐引擎不可用")
        
        # 生成今日推荐
        recommendations = engine.generate_daily_recommendations()
        
        if recommendations.get('status') == 'error':
            raise HTTPException(
                status_code=500, 
                detail=f"推荐生成失败: {recommendations.get('error', '未知错误')}"
            )
        
        return {
            "status": "success",
            "data": recommendations,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取今日推荐失败: {e}")
        raise HTTPException(status_code=500, detail=f"推荐服务异常: {str(e)}")

@router.get("/position/{position}")
async def get_position_recommendation(
    position: str,
    model_type: str = Query("xgboost", description="模型类型"),
    days: int = Query(30, description="历史数据分析天数", ge=7, le=90)
):
    """
    获取特定位置的推荐
    
    Args:
        position: 位置 (hundreds, tens, units)
        model_type: 模型类型 (xgboost, lightgbm, lstm, ensemble)
        days: 历史数据分析天数
        
    Returns:
        Dict: 位置推荐结果
    """
    try:
        # 验证参数
        if position not in ["hundreds", "tens", "units"]:
            raise HTTPException(
                status_code=400, 
                detail="位置参数必须是 hundreds, tens, 或 units"
            )
        
        if model_type not in ["xgboost", "lightgbm", "lstm", "ensemble"]:
            raise HTTPException(
                status_code=400,
                detail="模型类型必须是 xgboost, lightgbm, lstm, 或 ensemble"
            )
        
        engine = get_recommendation_engine()
        if engine is None:
            raise HTTPException(status_code=503, detail="智能推荐引擎不可用")
        
        # 生成位置推荐
        recommendation = engine.generate_position_recommendation(position)
        
        return {
            "status": "success",
            "position": position,
            "model_type": model_type,
            "analysis_days": days,
            "data": recommendation,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取{position}位置推荐失败: {e}")
        raise HTTPException(status_code=500, detail=f"位置推荐服务异常: {str(e)}")

@router.get("/heatmap/{position}")
async def get_number_heatmap(
    position: str,
    time_window: int = Query(30, description="时间窗口(天)", ge=7, le=90)
):
    """
    获取数字热度图数据
    
    Args:
        position: 位置 (hundreds, tens, units)
        time_window: 时间窗口
        
    Returns:
        Dict: 热度图数据
    """
    try:
        # 验证参数
        if position not in ["hundreds", "tens", "units"]:
            raise HTTPException(
                status_code=400, 
                detail="位置参数必须是 hundreds, tens, 或 units"
            )
        
        engine = get_recommendation_engine()
        if engine is None:
            raise HTTPException(status_code=503, detail="智能推荐引擎不可用")
        
        # 获取历史数据分析（包含热度信息）
        historical_analysis = engine._analyze_historical_data(position, time_window)
        
        # 转换为热度图格式
        number_probabilities = historical_analysis.get('number_probabilities', {})
        
        heatmap_data = []
        for number in range(10):
            probability = number_probabilities.get(number, 0.1)
            heat_score = probability * 100  # 转换为0-100的热度值
            
            heatmap_data.append({
                'number': number,
                'heat_score': round(heat_score, 1),
                'probability': round(probability, 3),
                'frequency': historical_analysis.get('number_frequencies', {}).get(number, 0)
            })
        
        return {
            "status": "success",
            "position": position,
            "time_window": time_window,
            "total_records": historical_analysis.get('total_records', 0),
            "heatmap_data": heatmap_data,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取{position}热度图失败: {e}")
        raise HTTPException(status_code=500, detail=f"热度图服务异常: {str(e)}")

@router.get("/confidence")
async def get_confidence_analysis():
    """
    获取预测信心度分析
    
    Returns:
        Dict: 信心度分析结果
    """
    try:
        engine = get_recommendation_engine()
        if engine is None:
            raise HTTPException(status_code=503, detail="智能推荐引擎不可用")
        
        # 生成今日推荐以获取信心度
        recommendations = engine.generate_daily_recommendations()
        
        if recommendations.get('status') == 'error':
            raise HTTPException(
                status_code=500, 
                detail="信心度分析失败"
            )
        
        # 提取信心度信息
        overall_confidence = recommendations.get('overall_confidence', 0.5)
        positions_confidence = {}
        
        for position, data in recommendations.get('positions', {}).items():
            if 'recommendations' in data and data['recommendations']:
                best_rec = data['recommendations'][0]
                positions_confidence[position] = {
                    'confidence': best_rec['confidence'],
                    'risk_level': best_rec['risk_level'],
                    'star_rating': best_rec['star_rating']
                }
        
        return {
            "status": "success",
            "overall_confidence": overall_confidence,
            "confidence_level": "高信心" if overall_confidence > 0.8 else 
                              "中等信心" if overall_confidence > 0.6 else "低信心",
            "positions_confidence": positions_confidence,
            "algorithm_version": recommendations.get('algorithm_version', 'unknown'),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取信心度分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"信心度分析服务异常: {str(e)}")

@router.get("/health")
async def health_check():
    """
    健康检查
    
    Returns:
        Dict: 健康状态
    """
    try:
        engine = get_recommendation_engine()
        
        return {
            "status": "healthy" if engine is not None else "unhealthy",
            "service": "智能推荐服务",
            "version": "v2.0.0",
            "recommendation_engine_available": engine is not None,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "service": "智能推荐服务",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
