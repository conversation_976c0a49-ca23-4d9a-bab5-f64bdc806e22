#!/usr/bin/env python3
"""
学习数据管理器
用于管理预测记录、准确率统计、策略配置等学习验证相关数据
"""

import sqlite3
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass


@dataclass
class PredictionRecord:
    """预测记录数据模型"""
    prediction_id: str
    issue: str
    predicted_numbers: str
    actual_numbers: Optional[str]
    accuracy_score: Optional[float]
    confidence_level: float
    prediction_date: str
    draw_date: Optional[str]
    model_source: str
    hit_positions: Optional[str]  # JSON格式存储命中位置
    strategy_id: Optional[str]
    metadata: Optional[str]  # JSON格式存储额外信息


@dataclass
class AccuracyStats:
    """准确率统计数据模型"""
    date: str
    position: str  # 'hundreds', 'tens', 'units', 'combination'
    accuracy_rate: float
    total_predictions: int
    correct_predictions: int
    strategy_id: Optional[str]
    time_period: str  # 'daily', 'weekly', 'monthly'


@dataclass
class StrategyConfig:
    """策略配置数据模型"""
    strategy_id: str
    strategy_name: str
    strategy_type: str
    parameters: str  # JSON格式存储参数
    is_active: bool
    created_date: str
    last_used: Optional[str]
    performance_score: Optional[float]


class LearningDataManager:
    """学习数据管理器"""
    
    def __init__(self, db_path: str = "data/fucai3d.db"):
        self.db_path = db_path
        self._init_tables()
    
    def get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def _init_tables(self):
        """初始化学习相关数据表"""
        with self.get_connection() as conn:
            # 预测记录表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS prediction_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    prediction_id TEXT UNIQUE NOT NULL,
                    issue TEXT NOT NULL,
                    predicted_numbers TEXT NOT NULL,
                    actual_numbers TEXT,
                    accuracy_score REAL,
                    confidence_level REAL NOT NULL,
                    prediction_date TIMESTAMP NOT NULL,
                    draw_date TIMESTAMP,
                    model_source TEXT NOT NULL,
                    hit_positions TEXT,
                    strategy_id TEXT,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 准确率统计表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS accuracy_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    position TEXT NOT NULL,
                    accuracy_rate REAL NOT NULL,
                    total_predictions INTEGER NOT NULL,
                    correct_predictions INTEGER NOT NULL,
                    strategy_id TEXT,
                    time_period TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(date, position, strategy_id, time_period)
                )
            """)
            
            # 策略配置表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS strategy_configs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_id TEXT UNIQUE NOT NULL,
                    strategy_name TEXT NOT NULL,
                    strategy_type TEXT NOT NULL,
                    parameters TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    created_date TIMESTAMP NOT NULL,
                    last_used TIMESTAMP,
                    performance_score REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建索引
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_prediction_records_issue ON prediction_records (issue)",
                "CREATE INDEX IF NOT EXISTS idx_prediction_records_date ON prediction_records (prediction_date)",
                "CREATE INDEX IF NOT EXISTS idx_prediction_records_model ON prediction_records (model_source)",
                "CREATE INDEX IF NOT EXISTS idx_accuracy_stats_date ON accuracy_stats (date)",
                "CREATE INDEX IF NOT EXISTS idx_accuracy_stats_position ON accuracy_stats (position)",
                "CREATE INDEX IF NOT EXISTS idx_strategy_configs_active ON strategy_configs (is_active)"
            ]
            
            for index_sql in indexes:
                conn.execute(index_sql)
            
            conn.commit()
    
    def insert_prediction_record(self, record: PredictionRecord) -> bool:
        """插入预测记录"""
        try:
            with self.get_connection() as conn:
                sql = """
                INSERT OR REPLACE INTO prediction_records 
                (prediction_id, issue, predicted_numbers, actual_numbers, accuracy_score,
                 confidence_level, prediction_date, draw_date, model_source, hit_positions,
                 strategy_id, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                conn.execute(sql, (
                    record.prediction_id, record.issue, record.predicted_numbers,
                    record.actual_numbers, record.accuracy_score, record.confidence_level,
                    record.prediction_date, record.draw_date, record.model_source,
                    record.hit_positions, record.strategy_id, record.metadata
                ))
                conn.commit()
                return True
        except Exception as e:
            print(f"插入预测记录失败: {e}")
            return False
    
    def update_prediction_result(self, prediction_id: str, actual_numbers: str, 
                               accuracy_score: float, hit_positions: List[str]) -> bool:
        """更新预测结果"""
        try:
            with self.get_connection() as conn:
                sql = """
                UPDATE prediction_records 
                SET actual_numbers = ?, accuracy_score = ?, hit_positions = ?, 
                    updated_at = CURRENT_TIMESTAMP
                WHERE prediction_id = ?
                """
                conn.execute(sql, (
                    actual_numbers, accuracy_score, json.dumps(hit_positions), prediction_id
                ))
                conn.commit()
                return True
        except Exception as e:
            print(f"更新预测结果失败: {e}")
            return False
    
    def get_prediction_records(self, limit: int = 50, offset: int = 0, 
                             filters: Optional[Dict] = None) -> List[Dict]:
        """获取预测记录列表"""
        try:
            with self.get_connection() as conn:
                where_clauses = []
                params = []
                
                if filters:
                    if filters.get('start_date'):
                        where_clauses.append("prediction_date >= ?")
                        params.append(filters['start_date'])
                    if filters.get('end_date'):
                        where_clauses.append("prediction_date <= ?")
                        params.append(filters['end_date'])
                    if filters.get('model_source'):
                        where_clauses.append("model_source = ?")
                        params.append(filters['model_source'])
                    if filters.get('has_result'):
                        where_clauses.append("actual_numbers IS NOT NULL")
                
                where_sql = " WHERE " + " AND ".join(where_clauses) if where_clauses else ""
                
                sql = f"""
                SELECT * FROM prediction_records 
                {where_sql}
                ORDER BY prediction_date DESC 
                LIMIT ? OFFSET ?
                """
                params.extend([limit, offset])
                
                cursor = conn.execute(sql, params)
                return [dict(row) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取预测记录失败: {e}")
            return []
    
    def get_accuracy_statistics(self, days: int = 30) -> Dict[str, Any]:
        """获取准确率统计"""
        try:
            with self.get_connection() as conn:
                start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
                
                # 总体统计
                cursor = conn.execute("""
                    SELECT 
                        COUNT(*) as total_predictions,
                        COUNT(CASE WHEN actual_numbers IS NOT NULL THEN 1 END) as completed_predictions,
                        AVG(CASE WHEN accuracy_score IS NOT NULL THEN accuracy_score END) as avg_accuracy,
                        COUNT(CASE WHEN accuracy_score = 1.0 THEN 1 END) as perfect_hits
                    FROM prediction_records 
                    WHERE prediction_date >= ?
                """, (start_date,))
                
                overall_stats = dict(cursor.fetchone())
                
                # 按位置统计
                position_stats = {}
                for position in ['hundreds', 'tens', 'units']:
                    cursor = conn.execute("""
                        SELECT 
                            AVG(accuracy_rate) as avg_accuracy,
                            SUM(total_predictions) as total_predictions,
                            SUM(correct_predictions) as correct_predictions
                        FROM accuracy_stats 
                        WHERE date >= ? AND position = ?
                    """, (start_date, position))
                    
                    result = cursor.fetchone()
                    if result:
                        position_stats[position] = dict(result)
                
                return {
                    'overall': overall_stats,
                    'by_position': position_stats,
                    'period': f'最近{days}天'
                }
        except Exception as e:
            print(f"获取准确率统计失败: {e}")
            return {}
