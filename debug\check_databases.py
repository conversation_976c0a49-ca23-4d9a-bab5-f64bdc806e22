#!/usr/bin/env python3
"""
数据库结构检查工具
用于分析fucai3d.db和lottery.db的实际结构和数据
"""

import sqlite3
import os
from datetime import datetime

def check_database(db_path, db_name):
    """检查数据库结构和数据"""
    print(f"\n{'='*50}")
    print(f"检查数据库: {db_name}")
    print(f"路径: {db_path}")
    print(f"{'='*50}")
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    file_size = os.path.getsize(db_path)
    print(f"📁 文件大小: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 获取所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [t[0] for t in cursor.fetchall()]
            print(f"📊 表数量: {len(tables)}")
            print(f"📋 表列表: {tables}")
            
            # 检查lottery_data表
            if 'lottery_data' in tables:
                print(f"\n🎯 lottery_data表分析:")
                
                # 记录数
                cursor.execute('SELECT COUNT(*) FROM lottery_data')
                count = cursor.fetchone()[0]
                print(f"  📈 记录数: {count:,}")
                
                if count > 0:
                    # 最新记录
                    try:
                        cursor.execute('''
                            SELECT issue, draw_date, hundreds, tens, units 
                            FROM lottery_data 
                            ORDER BY id DESC 
                            LIMIT 5
                        ''')
                        records = cursor.fetchall()
                        print(f"  📅 最新5条记录:")
                        for i, record in enumerate(records, 1):
                            print(f"    {i}. 期号:{record[0]}, 日期:{record[1]}, 号码:{record[2]}{record[3]}{record[4]}")
                    except Exception as e:
                        print(f"  ❌ 查询最新记录失败: {e}")
                    
                    # 2025年数据
                    try:
                        cursor.execute("SELECT COUNT(*) FROM lottery_data WHERE issue LIKE '2025%'")
                        count_2025 = cursor.fetchone()[0]
                        print(f"  🗓️ 2025年数据: {count_2025} 条")
                        
                        if count_2025 > 0:
                            cursor.execute('''
                                SELECT issue, draw_date, hundreds, tens, units 
                                FROM lottery_data 
                                WHERE issue LIKE '2025%'
                                ORDER BY issue DESC 
                                LIMIT 3
                            ''')
                            records_2025 = cursor.fetchall()
                            print(f"  📊 2025年最新3条:")
                            for record in records_2025:
                                print(f"    期号:{record[0]}, 日期:{record[1]}, 号码:{record[2]}{record[3]}{record[4]}")
                    except Exception as e:
                        print(f"  ❌ 查询2025年数据失败: {e}")
            else:
                print(f"\n❌ 没有lottery_data表")
                
                # 查找可能的数据表
                data_tables = [t for t in tables if 'lottery' in t.lower() or 'data' in t.lower()]
                if data_tables:
                    print(f"🔍 发现可能的数据表: {data_tables}")
                    for table in data_tables:
                        try:
                            cursor.execute(f'SELECT COUNT(*) FROM {table}')
                            count = cursor.fetchone()[0]
                            print(f"  📊 {table}: {count} 条记录")
                        except:
                            pass
            
            # 检查其他重要表
            important_tables = ['fusion_predictions', 'final_predictions', 'lottery_records']
            for table in important_tables:
                if table in tables:
                    try:
                        cursor.execute(f'SELECT COUNT(*) FROM {table}')
                        count = cursor.fetchone()[0]
                        print(f"📋 {table}表: {count} 条记录")
                    except:
                        pass
                        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

def main():
    """主函数"""
    print(f"🔍 福彩3D数据库结构检查")
    print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查两个主要数据库
    databases = [
        ("data/fucai3d.db", "主业务数据库"),
        ("data/lottery.db", "历史数据库"),
        ("data/fusion_predictions.db", "融合预测数据库"),
        ("data/alerts.db", "告警数据库")
    ]
    
    for db_path, db_name in databases:
        check_database(db_path, db_name)
    
    print(f"\n{'='*50}")
    print(f"✅ 数据库检查完成")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
