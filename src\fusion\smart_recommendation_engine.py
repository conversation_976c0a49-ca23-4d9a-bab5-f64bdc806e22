"""
智能推荐引擎
基于SHAP分析和历史数据提供智能的福彩3D号码推荐
"""

import sqlite3
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
import json
from pathlib import Path
import sys

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.fusion.prediction_explainer import PredictionExplainer
    from src.predictors.predictor_interface import PredictorFeatureInterface
except ImportError as e:
    logging.warning(f"导入依赖失败: {e}")

logger = logging.getLogger(__name__)

class SmartRecommendationEngine:
    """
    智能推荐引擎
    
    基于SHAP分析、历史数据趋势和多模型融合，
    为福彩3D预测提供智能的号码推荐服务。
    """
    
    def __init__(self, db_path: str = "data/fucai3d.db"):
        """
        初始化智能推荐引擎
        
        Args:
            db_path: 数据库路径
        """
        self.db_path = db_path
        self.explainer = None
        self.feature_interfaces = {}
        
        # 推荐算法版本
        self.algorithm_version = "v2.0.0"
        
        # 初始化组件
        self._init_components()
        
        logger.info("智能推荐引擎初始化完成")
    
    def _init_components(self):
        """初始化核心组件"""
        try:
            # 初始化SHAP解释器
            self.explainer = PredictionExplainer(self.db_path)
            
            # 初始化特征接口
            positions = ['hundreds', 'tens', 'units']
            for position in positions:
                self.feature_interfaces[position] = PredictorFeatureInterface(position)
                
            logger.info("智能推荐引擎组件初始化完成")
            
        except Exception as e:
            logger.error(f"组件初始化失败: {e}")
    
    def generate_daily_recommendations(self) -> Dict[str, Any]:
        """
        生成今日智能推荐
        
        Returns:
            Dict: 包含所有位置推荐的完整结果
        """
        try:
            recommendations = {}
            
            # 为每个位置生成推荐
            positions = ['hundreds', 'tens', 'units']
            for position in positions:
                position_rec = self.generate_position_recommendation(position)
                recommendations[position] = position_rec
            
            # 生成组合推荐
            combination_rec = self._generate_combination_recommendation(recommendations)
            
            # 计算整体信心度
            overall_confidence = self._calculate_overall_confidence(recommendations)
            
            return {
                'timestamp': datetime.now().isoformat(),
                'algorithm_version': self.algorithm_version,
                'positions': recommendations,
                'combination': combination_rec,
                'overall_confidence': overall_confidence,
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"生成今日推荐失败: {e}")
            return {
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                'error': str(e)
            }
    
    def generate_position_recommendation(self, position: str) -> Dict[str, Any]:
        """
        为特定位置生成推荐
        
        Args:
            position: 位置 (hundreds, tens, units)
            
        Returns:
            Dict: 位置推荐结果
        """
        try:
            # 获取历史数据分析
            historical_analysis = self._analyze_historical_data(position)
            
            # 获取SHAP特征重要性
            shap_analysis = self._get_shap_feature_importance(position)
            
            # 计算数字推荐度
            number_scores = self._calculate_number_scores(
                position, historical_analysis, shap_analysis
            )
            
            # 生成Top3推荐
            top_recommendations = self._get_top_recommendations(number_scores, top_n=3)
            
            # 生成推荐理由
            recommendations_with_reasons = []
            for rec in top_recommendations:
                reason = self._generate_recommendation_reason(
                    rec['number'], rec['score'], historical_analysis, shap_analysis
                )
                recommendations_with_reasons.append({
                    'number': rec['number'],
                    'confidence': rec['score'],
                    'reason': reason,
                    'risk_level': self._assess_risk_level(rec['score']),
                    'star_rating': self._calculate_star_rating(rec['score'])
                })
            
            return {
                'position': position,
                'recommendations': recommendations_with_reasons,
                'analysis_summary': {
                    'historical_trends': historical_analysis.get('summary', ''),
                    'shap_insights': shap_analysis.get('summary', ''),
                    'total_analyzed_numbers': len(number_scores)
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"生成{position}位置推荐失败: {e}")
            return self._generate_fallback_recommendation(position)
    
    def _analyze_historical_data(self, position: str, days: int = 30) -> Dict[str, Any]:
        """
        分析历史数据趋势
        
        Args:
            position: 位置
            days: 分析天数
            
        Returns:
            Dict: 历史数据分析结果
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                # 获取最近N天的数据
                query = f"""
                SELECT {position}, draw_date, sum_value, span
                FROM lottery_data 
                WHERE draw_date >= date('now', '-{days} days')
                ORDER BY draw_date DESC
                """
                
                df = pd.read_sql_query(query, conn)
                
                if df.empty:
                    return {'summary': '历史数据不足', 'trends': {}}
                
                # 分析数字出现频率
                number_counts = df[position].value_counts().to_dict()
                total_count = len(df)
                
                # 计算出现概率
                number_probabilities = {
                    num: count / total_count 
                    for num, count in number_counts.items()
                }
                
                # 分析趋势
                recent_numbers = df[position].head(10).tolist()
                trend_analysis = self._analyze_number_trends(recent_numbers)
                
                return {
                    'summary': f'最近{days}天数据分析完成',
                    'total_records': total_count,
                    'number_frequencies': number_counts,
                    'number_probabilities': number_probabilities,
                    'recent_trend': trend_analysis,
                    'analysis_period': days
                }
                
        except Exception as e:
            logger.error(f"历史数据分析失败: {e}")
            return {'summary': '历史数据分析失败', 'error': str(e)}
    
    def _get_shap_feature_importance(self, position: str) -> Dict[str, Any]:
        """
        获取SHAP特征重要性分析
        
        Args:
            position: 位置
            
        Returns:
            Dict: SHAP分析结果
        """
        try:
            if not self.explainer:
                return {'summary': 'SHAP解释器不可用', 'features': []}
            
            # 获取特征重要性
            feature_importance = self.explainer.get_feature_importance_api(
                position=position,
                model_type='xgboost',
                top_n=10
            )
            
            if feature_importance.get('status') == 'success':
                features = feature_importance.get('feature_importance', [])
                
                # 提取关键特征
                key_features = []
                for feature in features[:5]:  # Top 5特征
                    key_features.append({
                        'name': feature.get('feature_name', ''),
                        'importance': feature.get('importance', 0),
                        'impact': self._interpret_feature_impact(
                            feature.get('feature_name', ''),
                            feature.get('importance', 0)
                        )
                    })
                
                return {
                    'summary': f'SHAP分析识别出{len(features)}个重要特征',
                    'key_features': key_features,
                    'total_features': len(features)
                }
            else:
                return {'summary': 'SHAP分析失败', 'features': []}
                
        except Exception as e:
            logger.error(f"SHAP特征重要性获取失败: {e}")
            return {'summary': 'SHAP分析异常', 'error': str(e)}

    def _calculate_number_scores(self, position: str, historical_analysis: Dict,
                                shap_analysis: Dict) -> Dict[int, float]:
        """
        计算0-9每个数字的推荐度评分

        Args:
            position: 位置
            historical_analysis: 历史数据分析结果
            shap_analysis: SHAP分析结果

        Returns:
            Dict: 数字推荐度评分 {number: score}
        """
        try:
            number_scores = {}

            # 获取历史概率
            historical_probs = historical_analysis.get('number_probabilities', {})

            # 基础评分：基于历史概率
            for number in range(10):
                base_score = historical_probs.get(number, 0.1)  # 默认0.1

                # SHAP特征影响调整
                shap_adjustment = self._calculate_shap_adjustment(number, shap_analysis)

                # 趋势调整
                trend_adjustment = self._calculate_trend_adjustment(
                    number, historical_analysis.get('recent_trend', {})
                )

                # 综合评分
                final_score = base_score * (1 + shap_adjustment + trend_adjustment)

                # 确保评分在合理范围内
                final_score = max(0.0, min(1.0, final_score))

                number_scores[number] = final_score

            return number_scores

        except Exception as e:
            logger.error(f"计算数字评分失败: {e}")
            # 返回均匀分布作为备选
            return {i: 0.1 for i in range(10)}

    def _calculate_shap_adjustment(self, number: int, shap_analysis: Dict) -> float:
        """
        基于SHAP分析计算评分调整

        Args:
            number: 数字
            shap_analysis: SHAP分析结果

        Returns:
            float: 调整系数 (-0.5 到 0.5)
        """
        try:
            key_features = shap_analysis.get('key_features', [])

            adjustment = 0.0
            for feature in key_features:
                feature_name = feature.get('name', '')
                importance = feature.get('importance', 0)

                # 根据特征名称和重要性计算对特定数字的影响
                if '十位' in feature_name or 'tens' in feature_name.lower():
                    # 十位特征对当前数字的影响
                    adjustment += importance * 0.1
                elif '个位' in feature_name or 'units' in feature_name.lower():
                    # 个位特征对当前数字的影响
                    adjustment += importance * 0.1
                elif '和值' in feature_name or 'sum' in feature_name.lower():
                    # 和值特征的影响
                    adjustment += importance * 0.05

            # 限制调整范围
            return max(-0.5, min(0.5, adjustment))

        except Exception as e:
            logger.error(f"SHAP调整计算失败: {e}")
            return 0.0

    def _calculate_trend_adjustment(self, number: int, trend_analysis: Dict) -> float:
        """
        基于趋势分析计算评分调整

        Args:
            number: 数字
            trend_analysis: 趋势分析结果

        Returns:
            float: 调整系数 (-0.3 到 0.3)
        """
        try:
            # 简单的趋势分析：如果数字在最近出现频率高，给予正向调整
            recent_frequency = trend_analysis.get('recent_frequency', {})
            avg_frequency = trend_analysis.get('average_frequency', 0.1)

            number_freq = recent_frequency.get(number, avg_frequency)

            if number_freq > avg_frequency * 1.2:
                return 0.2  # 正向调整
            elif number_freq < avg_frequency * 0.8:
                return -0.1  # 负向调整
            else:
                return 0.0  # 无调整

        except Exception as e:
            logger.error(f"趋势调整计算失败: {e}")
            return 0.0

    def _analyze_number_trends(self, recent_numbers: List[int]) -> Dict[str, Any]:
        """
        分析数字趋势

        Args:
            recent_numbers: 最近的数字列表

        Returns:
            Dict: 趋势分析结果
        """
        try:
            if not recent_numbers:
                return {'recent_frequency': {}, 'average_frequency': 0.1}

            # 计算最近数字频率
            from collections import Counter
            recent_counter = Counter(recent_numbers)
            total_recent = len(recent_numbers)

            recent_frequency = {
                num: count / total_recent
                for num, count in recent_counter.items()
            }

            # 补充未出现的数字
            for i in range(10):
                if i not in recent_frequency:
                    recent_frequency[i] = 0.0

            average_frequency = 1.0 / 10  # 理论平均频率

            return {
                'recent_frequency': recent_frequency,
                'average_frequency': average_frequency,
                'total_recent_count': total_recent
            }

        except Exception as e:
            logger.error(f"趋势分析失败: {e}")
            return {'recent_frequency': {}, 'average_frequency': 0.1}

    def _get_top_recommendations(self, number_scores: Dict[int, float],
                                top_n: int = 3) -> List[Dict[str, Any]]:
        """
        获取Top N推荐

        Args:
            number_scores: 数字评分
            top_n: 返回数量

        Returns:
            List: Top N推荐列表
        """
        try:
            # 按评分排序
            sorted_numbers = sorted(
                number_scores.items(),
                key=lambda x: x[1],
                reverse=True
            )

            top_recommendations = []
            for i, (number, score) in enumerate(sorted_numbers[:top_n]):
                top_recommendations.append({
                    'number': number,
                    'score': score,
                    'rank': i + 1
                })

            return top_recommendations

        except Exception as e:
            logger.error(f"获取Top推荐失败: {e}")
            return [{'number': i, 'score': 0.1, 'rank': i+1} for i in range(min(top_n, 3))]

    def _generate_recommendation_reason(self, number: int, score: float,
                                      historical_analysis: Dict, shap_analysis: Dict) -> str:
        """
        生成推荐理由

        Args:
            number: 推荐数字
            score: 推荐评分
            historical_analysis: 历史分析
            shap_analysis: SHAP分析

        Returns:
            str: 推荐理由
        """
        try:
            reasons = []

            # 基于历史频率的理由
            historical_probs = historical_analysis.get('number_probabilities', {})
            number_prob = historical_probs.get(number, 0.1)

            if number_prob > 0.15:
                reasons.append(f"近期出现频率较高({number_prob:.1%})")
            elif number_prob < 0.05:
                reasons.append(f"长期未出现，具有补偿性")
            else:
                reasons.append(f"出现频率适中({number_prob:.1%})")

            # 基于SHAP分析的理由
            key_features = shap_analysis.get('key_features', [])
            if key_features:
                top_feature = key_features[0]
                feature_name = top_feature.get('name', '')
                if feature_name:
                    reasons.append(f"关键特征'{feature_name}'支持此预测")

            # 基于评分的理由
            if score > 0.8:
                reasons.append("AI模型高度推荐")
            elif score > 0.6:
                reasons.append("AI模型推荐")
            else:
                reasons.append("AI模型谨慎推荐")

            return "；".join(reasons)

        except Exception as e:
            logger.error(f"生成推荐理由失败: {e}")
            return f"基于AI分析推荐数字{number}"

    def _assess_risk_level(self, score: float) -> str:
        """
        评估风险等级

        Args:
            score: 推荐评分

        Returns:
            str: 风险等级
        """
        if score >= 0.8:
            return "低风险"
        elif score >= 0.6:
            return "中等风险"
        else:
            return "高风险"

    def _calculate_star_rating(self, score: float) -> int:
        """
        计算星级评分

        Args:
            score: 推荐评分

        Returns:
            int: 星级 (1-5)
        """
        if score >= 0.9:
            return 5
        elif score >= 0.8:
            return 4
        elif score >= 0.6:
            return 3
        elif score >= 0.4:
            return 2
        else:
            return 1

    def _interpret_feature_impact(self, feature_name: str, importance: float) -> str:
        """
        解释特征影响

        Args:
            feature_name: 特征名称
            importance: 重要性

        Returns:
            str: 影响解释
        """
        try:
            if importance > 0.3:
                impact_level = "强烈影响"
            elif importance > 0.2:
                impact_level = "显著影响"
            elif importance > 0.1:
                impact_level = "中等影响"
            else:
                impact_level = "轻微影响"

            return f"{feature_name}对预测有{impact_level}"

        except Exception as e:
            logger.error(f"特征影响解释失败: {e}")
            return f"{feature_name}影响预测"

    def _generate_combination_recommendation(self, position_recommendations: Dict) -> Dict[str, Any]:
        """
        生成组合推荐

        Args:
            position_recommendations: 各位置推荐结果

        Returns:
            Dict: 组合推荐结果
        """
        try:
            # 获取各位置的最佳推荐
            best_combination = []
            total_confidence = 0.0

            positions = ['hundreds', 'tens', 'units']
            for position in positions:
                pos_rec = position_recommendations.get(position, {})
                recommendations = pos_rec.get('recommendations', [])

                if recommendations:
                    best_rec = recommendations[0]  # 第一个是最佳推荐
                    best_combination.append(best_rec['number'])
                    total_confidence += best_rec['confidence']
                else:
                    best_combination.append(0)  # 默认值

            # 计算组合信心度
            avg_confidence = total_confidence / len(positions) if positions else 0.0

            # 生成组合号码
            combination_number = ''.join(map(str, best_combination))

            return {
                'combination': combination_number,
                'numbers': best_combination,
                'confidence': avg_confidence,
                'risk_level': self._assess_risk_level(avg_confidence),
                'star_rating': self._calculate_star_rating(avg_confidence),
                'recommendation': f"推荐组合{combination_number}，综合信心度{avg_confidence:.1%}"
            }

        except Exception as e:
            logger.error(f"生成组合推荐失败: {e}")
            return {
                'combination': '000',
                'numbers': [0, 0, 0],
                'confidence': 0.5,
                'risk_level': '中等风险',
                'star_rating': 3,
                'recommendation': '组合推荐生成失败，请重试'
            }

    def _calculate_overall_confidence(self, position_recommendations: Dict) -> float:
        """
        计算整体信心度

        Args:
            position_recommendations: 各位置推荐结果

        Returns:
            float: 整体信心度
        """
        try:
            total_confidence = 0.0
            valid_positions = 0

            for position, rec in position_recommendations.items():
                recommendations = rec.get('recommendations', [])
                if recommendations:
                    # 使用最佳推荐的信心度
                    total_confidence += recommendations[0]['confidence']
                    valid_positions += 1

            if valid_positions > 0:
                return total_confidence / valid_positions
            else:
                return 0.5  # 默认中等信心度

        except Exception as e:
            logger.error(f"计算整体信心度失败: {e}")
            return 0.5

    def _generate_fallback_recommendation(self, position: str) -> Dict[str, Any]:
        """
        生成备用推荐（当主要算法失败时）

        Args:
            position: 位置

        Returns:
            Dict: 备用推荐结果
        """
        try:
            # 简单的备用推荐逻辑
            import random

            # 生成3个随机推荐
            recommendations = []
            used_numbers = set()

            for i in range(3):
                while True:
                    number = random.randint(0, 9)
                    if number not in used_numbers:
                        used_numbers.add(number)
                        break

                recommendations.append({
                    'number': number,
                    'confidence': 0.3 + random.random() * 0.4,  # 0.3-0.7
                    'reason': f"基于随机算法推荐数字{number}",
                    'risk_level': '中等风险',
                    'star_rating': 3
                })

            return {
                'position': position,
                'recommendations': recommendations,
                'analysis_summary': {
                    'historical_trends': '使用备用推荐算法',
                    'shap_insights': '主要算法不可用',
                    'total_analyzed_numbers': 10
                },
                'timestamp': datetime.now().isoformat(),
                'fallback': True
            }

        except Exception as e:
            logger.error(f"生成备用推荐失败: {e}")
            return {
                'position': position,
                'recommendations': [
                    {'number': 0, 'confidence': 0.5, 'reason': '系统默认推荐', 'risk_level': '中等风险', 'star_rating': 3}
                ],
                'analysis_summary': {'historical_trends': '系统异常', 'shap_insights': '分析失败'},
                'timestamp': datetime.now().isoformat(),
                'error': True
            }
