# SHAP组件Tabs修复计划

## 问题描述
- Tabs.TabPane 已弃用，需要使用 items API
- rc-collapse children 将被移除，需要使用 items API

## 修复策略

### 1. Tabs组件修复
将现有的TabPane结构转换为items数组格式：

```typescript
// 旧格式
<Tabs defaultActiveKey="1">
  <TabPane tab={<span><Icon />标题</span>} key="1">
    内容
  </TabPane>
</Tabs>

// 新格式
const tabItems: TabsProps['items'] = [
  {
    key: '1',
    label: <span><Icon />标题</span>,
    children: 内容
  }
]

<Tabs defaultActiveKey="1" items={tabItems} />
```

### 2. Collapse组件修复
将现有的Panel结构转换为items数组格式：

```typescript
// 旧格式
<Collapse>
  <Panel header="标题" key="1">
    内容
  </Panel>
</Collapse>

// 新格式
const collapseItems = [
  {
    key: '1',
    label: '标题',
    children: 内容
  }
]

<Collapse items={collapseItems} />
```

## 实施步骤

1. 修复ShapExplainer.tsx中的Tabs组件
2. 修复ShapUserGuide.tsx中的Collapse组件
3. 测试所有功能正常工作
4. 验证警告消失

## 预期结果
- 消除所有弃用警告
- 保持现有功能完全正常
- 提升代码的未来兼容性
